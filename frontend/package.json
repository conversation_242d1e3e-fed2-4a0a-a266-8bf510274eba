{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@heroicons/react": "^2.2.0", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "axios": "^1.8.4", "cmdk": "^1.1.1", "cra-template": "1.2.0", "framer-motion": "^12.12.1", "lucide-react": "^0.511.0", "react": "^19.0.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^19.0.0", "react-error-boundary": "^4.0.13", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.5.1", "react-scripts": "5.0.1", "react-scroll-parallax": "^3.4.5", "react-social-icons": "^6.24.0", "typescript": "^5.8.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "predeploy": "yarn build", "deploy": "gh-pages -d build -b gh-pages", "test:e2e": "playwright test", "lint": "eslint src", "lint:fix": "eslint src --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\""}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/?(*.)(spec|test).{js,jsx,ts,tsx}"]}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@babel/core": "^7.24.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.24.0", "@babel/preset-typescript": "^7.24.0", "@eslint/js": "9.23.0", "@playwright/test": "^1.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/react-copy-to-clipboard": "^5.0.7", "@typescript-eslint/eslint-plugin": "8.33.0", "@typescript-eslint/parser": "8.33.0", "autoprefixer": "^10.4.20", "babel-jest": "^29.7.0", "eslint": "9.23.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.4", "eslint-plugin-tailwindcss": "3.18.0", "gh-pages": "^6.1.1", "globals": "15.15.0", "identity-obj-proxy": "^3.0.0", "jest-transform-stub": "^2.0.0", "postcss": "^8.5.0", "serve": "^14.2.4", "tailwindcss": "^3.4.17"}}